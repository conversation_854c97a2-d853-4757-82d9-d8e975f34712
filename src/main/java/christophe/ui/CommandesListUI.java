package christophe.ui;

import christophe.DAO.CommandeDAO;
import christophe.commande.Commande;
import christophe.Carte.Plat;

import javax.swing.*;
import javax.swing.table.DefaultTableCellRenderer;
import javax.swing.table.DefaultTableModel;
import java.awt.*;
import java.awt.event.*;
import java.text.SimpleDateFormat;
import java.util.List;

public class CommandesListUI extends JFrame {
    private CommandeDAO commandeDAO;
    private JTable commandesTableDaily;
    private JTable commandesTableAll;
    private DefaultTableModel tableModelDaily;
    private DefaultTableModel tableModelAll;
    private JTabbedPane tabbedPane;

    private static final String[] COLUMN_NAMES = {"ID", "Table", "Date", "Total", "Payée"};

    public CommandesListUI() {
        commandeDAO = new CommandeDAO();
        initUI();
    }

    private void initUI() {
        setTitle("Liste des Commandes");
        setSize(800, 600);
        setLocationRelativeTo(null);
        setDefaultCloseOperation(DISPOSE_ON_CLOSE);

        // Créer le modèle de table une seule fois
        tableModel = new DefaultTableModel(COLUMN_NAMES, 0) {
            @Override
            public boolean isCellEditable(int row, int column) {
                return false; // Rendre toutes les cellules non éditables
            }

            @Override
            public Class<?> getColumnClass(int columnIndex) {
                if (columnIndex == 4) { // Colonne "Payée"
                    return Boolean.class;
                }
                return String.class;
            }
        };

        // Créer la table une seule fois
        commandesTable = new JTable(tableModel);
        commandesTable.setSelectionMode(ListSelectionModel.SINGLE_SELECTION);
        commandesTable.setRowHeight(25);

        // Configurer le rendu des cellules
        setupTableRenderers();

        // Ajouter un listener pour le double-clic
        commandesTable.addMouseListener(new MouseAdapter() {
            @Override
            public void mouseClicked(MouseEvent e) {
                if (e.getClickCount() == 2) {
                    int row = commandesTable.getSelectedRow();
                    if (row >= 0) {
                        int commandeId = Integer.parseInt(tableModel.getValueAt(row, 0).toString());
                        showCommandeDetails(commandeId);
                    }
                }
            }
        });

        tabbedPane = new JTabbedPane();

        // Créer le panel principal avec la table
        JPanel mainPanel = createMainPanel();

        // Ajouter les onglets (ils partageront le même contenu)
        tabbedPane.addTab("Commandes du jour", mainPanel);
        tabbedPane.addTab("Toutes les commandes", mainPanel);

        // Ajouter un listener pour mettre à jour les données lors du changement d'onglet
        tabbedPane.addChangeListener(e -> {
            int selectedIndex = tabbedPane.getSelectedIndex();
            showingDailyOrders = selectedIndex == 0;
            System.out.println("Changement d'onglet - Index: " + selectedIndex + ", showingDailyOrders: " + showingDailyOrders);
            refreshTableData();
        });

        add(tabbedPane);
    }

    private void setupTableRenderers() {
        // Configurer le rendu des cellules avec couleurs alternées et commandes non payées en rouge
        commandesTable.setDefaultRenderer(Object.class, new DefaultTableCellRenderer() {
            @Override
            public Component getTableCellRendererComponent(JTable table, Object value,
                    boolean isSelected, boolean hasFocus, int row, int column) {
                Component c = super.getTableCellRendererComponent(table, value, isSelected, hasFocus, row, column);

                // Couleurs alternées pour les lignes
                if (!isSelected) {
                    c.setBackground(row % 2 == 0 ? Color.WHITE : new Color(240, 240, 240));

                    // Commandes non payées en rouge
                    Boolean isPaid = (Boolean) table.getValueAt(row, 4);
                    if (!isPaid) {
                        c.setForeground(Color.RED);
                    } else {
                        c.setForeground(Color.BLACK);
                    }
                }

                return c;
            }
        });

        // Configurer le rendu des booléens (colonne "Payée")
        commandesTable.setDefaultRenderer(Boolean.class, new DefaultTableCellRenderer() {
            @Override
            public Component getTableCellRendererComponent(JTable table, Object value,
                    boolean isSelected, boolean hasFocus, int row, int column) {
                JCheckBox checkBox = new JCheckBox();
                checkBox.setSelected((Boolean) value);
                checkBox.setHorizontalAlignment(JLabel.CENTER);

                // Couleurs alternées pour les lignes
                if (!isSelected) {
                    checkBox.setBackground(row % 2 == 0 ? Color.WHITE : new Color(240, 240, 240));

                    // Commandes non payées en rouge
                    if (!(Boolean) value) {
                        checkBox.setForeground(Color.RED);
                    }
                }

                return checkBox;
            }
        });
    }

    private JPanel createMainPanel() {
        JPanel panel = new JPanel(new BorderLayout());

        // Ajouter un label d'information en haut
        JLabel infoLabel = new JLabel("Chargement...", JLabel.CENTER);
        infoLabel.setBorder(BorderFactory.createEmptyBorder(5, 5, 5, 5));
        panel.add(infoLabel, BorderLayout.NORTH);

        // Ajouter la table à un JScrollPane
        JScrollPane scrollPane = new JScrollPane(commandesTable);
        panel.add(scrollPane, BorderLayout.CENTER);

        // Ajouter un bouton de rafraîchissement
        JButton refreshButton = new JButton("Rafraîchir");
        refreshButton.addActionListener(e -> refreshTableData());

        // Ajouter un bouton de test pour la méthode alternative
        JButton testButton = new JButton("Test 24h");
        testButton.addActionListener(e -> testAlternativeMethod());

        JPanel buttonPanel = new JPanel();
        buttonPanel.add(refreshButton);
        buttonPanel.add(testButton);
        panel.add(buttonPanel, BorderLayout.SOUTH);

        // Stocker le label d'information pour pouvoir le mettre à jour
        this.infoLabel = infoLabel;

        return panel;
    }



    private void refreshTableData() {
        System.out.println("=== REFRESH TABLE DATA ===");
        System.out.println("showingDailyOrders: " + showingDailyOrders);

        tableModel.setRowCount(0); // Vider la table

        List<Commande> commandes;
        if (showingDailyOrders) {
            System.out.println("Récupération des commandes du jour...");
            commandes = commandeDAO.getCommandesDuJour();
        } else {
            System.out.println("Récupération de toutes les commandes...");
            commandes = commandeDAO.getToutesCommandes();
        }

        System.out.println("Nombre de commandes récupérées: " + commandes.size());

        SimpleDateFormat dateFormat = new SimpleDateFormat("dd/MM/yyyy HH:mm");

        int rowCount = 0;
        for (Commande commande : commandes) {
            Object[] rowData = {
                commande.getId(),
                commande.getTableNumber(),
                dateFormat.format(commande.getDateCommande()),
                String.format("%.2f€", commande.getTotalCommande()),
                commande.isPayee()
            };
            tableModel.addRow(rowData);
            rowCount++;
            System.out.println("Ajout ligne " + rowCount + ": Commande #" + commande.getId() +
                             " - Table " + commande.getTableNumber() +
                             " - Date " + dateFormat.format(commande.getDateCommande()));
        }

        System.out.println("Nombre de lignes ajoutées au tableau: " + rowCount);
        System.out.println("Nombre de lignes dans le tableModel: " + tableModel.getRowCount());
        System.out.println("========================");

        // Mettre à jour le label d'information
        if (infoLabel != null) {
            String labelText = showingDailyOrders ?
                "Commandes du jour (" + rowCount + " commandes)" :
                "Toutes les commandes (" + rowCount + " commandes)";
            infoLabel.setText(labelText);
        }
    }

    private void showCommandeDetails(int commandeId) {
        // Trouver la commande correspondante
        List<Commande> commandes;
        if (showingDailyOrders) {
            commandes = commandeDAO.getCommandesDuJour();
        } else {
            commandes = commandeDAO.getToutesCommandes();
        }

        Commande selectedCommande = null;
        for (Commande commande : commandes) {
            if (commande.getId() == commandeId) {
                selectedCommande = commande;
                break;
            }
        }

        if (selectedCommande == null) {
            JOptionPane.showMessageDialog(this, "Commande introuvable", "Erreur", JOptionPane.ERROR_MESSAGE);
            return;
        }

        // Créer une copie finale de la commande sélectionnée pour utilisation dans les lambdas
        final Commande finalSelectedCommande = selectedCommande;

        // Créer une fenêtre modale pour afficher les détails
        JDialog detailsDialog = new JDialog(this, "Détails de la commande #" + commandeId, true);
        detailsDialog.setSize(500, 400);
        detailsDialog.setLocationRelativeTo(this);
        detailsDialog.setLayout(new BorderLayout());

        // Panel d'informations générales
        JPanel infoPanel = new JPanel(new GridLayout(3, 2, 10, 5));
        infoPanel.setBorder(BorderFactory.createEmptyBorder(10, 10, 10, 10));

        SimpleDateFormat dateFormat = new SimpleDateFormat("dd/MM/yyyy HH:mm");

        infoPanel.add(new JLabel("Numéro de table:"));
        infoPanel.add(new JLabel(String.valueOf(finalSelectedCommande.getTableNumber())));

        infoPanel.add(new JLabel("Date:"));
        infoPanel.add(new JLabel(dateFormat.format(finalSelectedCommande.getDateCommande())));

        infoPanel.add(new JLabel("Total:"));
        infoPanel.add(new JLabel(String.format("%.2f€", finalSelectedCommande.getTotalCommande())));

        detailsDialog.add(infoPanel, BorderLayout.NORTH);

        // Liste des plats
        DefaultTableModel platsModel = new DefaultTableModel(
                new String[]{"Plat", "Prix"}, 0);

        for (Plat plat : finalSelectedCommande.getMenus()) {
            platsModel.addRow(new Object[]{
                plat.getName(),
                String.format("%.2f€", plat.getPrice())
            });
        }

        JTable platsTable = new JTable(platsModel);
        platsTable.setRowHeight(25);

        JScrollPane platsScrollPane = new JScrollPane(platsTable);
        platsScrollPane.setBorder(BorderFactory.createTitledBorder("Plats commandés"));

        detailsDialog.add(platsScrollPane, BorderLayout.CENTER);

        // Panel de boutons
        JPanel buttonPanel = new JPanel();

        JCheckBox paidCheckBox = new JCheckBox("Commande payée", finalSelectedCommande.isPayee());
        buttonPanel.add(paidCheckBox);

        JButton saveButton = new JButton("Enregistrer");
        saveButton.addActionListener(e -> {
            boolean newPaidStatus = paidCheckBox.isSelected();
            if (newPaidStatus != finalSelectedCommande.isPayee()) {
                boolean success = commandeDAO.updateCommandePayee(commandeId, newPaidStatus);
                if (success) {
                    JOptionPane.showMessageDialog(detailsDialog,
                            "Statut de paiement mis à jour avec succès",
                            "Succès", JOptionPane.INFORMATION_MESSAGE);
                    refreshTableData();
                    detailsDialog.dispose();
                } else {
                    JOptionPane.showMessageDialog(detailsDialog,
                            "Erreur lors de la mise à jour du statut de paiement",
                            "Erreur", JOptionPane.ERROR_MESSAGE);
                }
            } else {
                detailsDialog.dispose();
            }
        });
        buttonPanel.add(saveButton);

        JButton cancelButton = new JButton("Annuler");
        cancelButton.addActionListener(e -> detailsDialog.dispose());
        buttonPanel.add(cancelButton);

        detailsDialog.add(buttonPanel, BorderLayout.SOUTH);

        detailsDialog.setVisible(true);
    }

    private void testAlternativeMethod() {
        System.out.println("=== TEST DE LA MÉTHODE ALTERNATIVE ===");
        List<Commande> commandesAlternative = commandeDAO.getCommandesDuJourAlternative();
        System.out.println("Nombre de commandes trouvées avec la méthode alternative: " + commandesAlternative.size());

        for (Commande commande : commandesAlternative) {
            System.out.println("Commande #" + commande.getId() +
                             " - Table: " + commande.getTableNumber() +
                             " - Date: " + commande.getDateCommande() +
                             " - Total: " + commande.getTotalCommande() + "€");
        }
        System.out.println("======================================");

        // Afficher un message à l'utilisateur
        JOptionPane.showMessageDialog(this,
            "Test terminé. " + commandesAlternative.size() + " commandes trouvées avec la méthode alternative.\n" +
            "Consultez la console pour plus de détails.",
            "Test Méthode Alternative",
            JOptionPane.INFORMATION_MESSAGE);
    }

    public void showUI() {
        refreshTableData();
        setVisible(true);
    }
}
