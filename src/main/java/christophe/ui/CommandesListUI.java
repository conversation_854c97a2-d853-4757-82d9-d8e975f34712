package christophe.ui;

import christophe.DAO.CommandeDAO;
import christophe.commande.Commande;
import christophe.Carte.Plat;

import javax.swing.*;
import javax.swing.table.DefaultTableCellRenderer;
import javax.swing.table.DefaultTableModel;
import java.awt.*;
import java.awt.event.*;
import java.text.SimpleDateFormat;
import java.util.List;

public class CommandesListUI extends JFrame {
    private static CommandesListUI instance;
    private CommandeDAO commandeDAO;
    private JTable commandesTable;
    private DefaultTableModel tableModel;
    private boolean showingDailyOrders;

    private static final String[] COLUMN_NAMES = {"ID", "Table", "Date", "Total", "Payée"};

    private CommandesListUI() {
        commandeDAO = new CommandeDAO();
        initUI();
    }

    public static CommandesListUI getInstance() {
        if (instance == null) {
            instance = new CommandesListUI();
        }
        return instance;
    }

    public static void showInstance() {
        CommandesListUI ui = getInstance();
        if (ui.isVisible()) {
            ui.toFront();
            ui.requestFocus();
        } else {
            ui.showUI();
        }
    }

    private void initUI() {
        setTitle("Liste des Commandes");
        setSize(800, 600);
        setLocationRelativeTo(null);
        setDefaultCloseOperation(DISPOSE_ON_CLOSE);

        // Créer directement le panel des commandes (toutes les commandes)
        JPanel commandesPanel = createCommandesPanel(false);
        showingDailyOrders = false; // Toujours afficher toutes les commandes

        add(commandesPanel);
    }

    private JPanel createCommandesPanel(boolean dailyOnly) {
        JPanel panel = new JPanel(new BorderLayout());

        // Créer le modèle de table
        tableModel = new DefaultTableModel(COLUMN_NAMES, 0) {
            @Override
            public boolean isCellEditable(int row, int column) {
                return false; // Rendre toutes les cellules non éditables
            }

            @Override
            public Class<?> getColumnClass(int columnIndex) {
                if (columnIndex == 4) { // Colonne "Payée"
                    return Boolean.class;
                }
                return String.class;
            }
        };

        // Créer la table
        commandesTable = new JTable(tableModel);
        commandesTable.setSelectionMode(ListSelectionModel.SINGLE_SELECTION);
        commandesTable.setRowHeight(25);

        // Configurer le rendu des cellules avec couleurs alternées et commandes non payées en rouge
        commandesTable.setDefaultRenderer(Object.class, new DefaultTableCellRenderer() {
            @Override
            public Component getTableCellRendererComponent(JTable table, Object value,
                    boolean isSelected, boolean hasFocus, int row, int column) {
                Component c = super.getTableCellRendererComponent(table, value, isSelected, hasFocus, row, column);

                // Couleurs alternées pour les lignes
                if (!isSelected) {
                    c.setBackground(row % 2 == 0 ? Color.WHITE : new Color(240, 240, 240));

                    // Commandes non payées en rouge
                    Boolean isPaid = (Boolean) table.getValueAt(row, 4);
                    if (!isPaid) {
                        c.setForeground(Color.RED);
                    } else {
                        c.setForeground(Color.BLACK);
                    }
                }

                return c;
            }
        });

        // Configurer le rendu des booléens (colonne "Payée")
        commandesTable.setDefaultRenderer(Boolean.class, new DefaultTableCellRenderer() {
            @Override
            public Component getTableCellRendererComponent(JTable table, Object value,
                    boolean isSelected, boolean hasFocus, int row, int column) {
                JCheckBox checkBox = new JCheckBox();
                checkBox.setSelected((Boolean) value);
                checkBox.setHorizontalAlignment(JLabel.CENTER);

                // Couleurs alternées pour les lignes
                if (!isSelected) {
                    checkBox.setBackground(row % 2 == 0 ? Color.WHITE : new Color(240, 240, 240));

                    // Commandes non payées en rouge
                    if (!(Boolean) value) {
                        checkBox.setForeground(Color.RED);
                    }
                }

                return checkBox;
            }
        });

        // Ajouter un listener pour le double-clic
        commandesTable.addMouseListener(new MouseAdapter() {
            @Override
            public void mouseClicked(MouseEvent e) {
                if (e.getClickCount() == 2) {
                    int row = commandesTable.getSelectedRow();
                    if (row >= 0) {
                        int commandeId = Integer.parseInt(tableModel.getValueAt(row, 0).toString());
                        showCommandeDetails(commandeId);
                    }
                }
            }
        });

        // Ajouter la table à un JScrollPane
        JScrollPane scrollPane = new JScrollPane(commandesTable);
        panel.add(scrollPane, BorderLayout.CENTER);

        // Ajouter un bouton de rafraîchissement
        JButton refreshButton = new JButton("Rafraîchir");
        refreshButton.addActionListener(e -> refreshTableData());

        JPanel buttonPanel = new JPanel();
        buttonPanel.add(refreshButton);
        panel.add(buttonPanel, BorderLayout.SOUTH);

        return panel;
    }

    private void refreshTableData() {
        tableModel.setRowCount(0); // Vider la table

        List<Commande> commandes = commandeDAO.getToutesCommandes();

        SimpleDateFormat dateFormat = new SimpleDateFormat("dd/MM/yyyy HH:mm");

        for (Commande commande : commandes) {
            Object[] rowData = {
                commande.getId(),
                commande.getTableNumber(),
                dateFormat.format(commande.getDateCommande()),
                String.format("%.2f€", commande.getTotalCommande()),
                commande.isPayee()
            };
            tableModel.addRow(rowData);
        }
    }

    private void showCommandeDetails(int commandeId) {
        // Trouver la commande correspondante
        List<Commande> commandes = commandeDAO.getToutesCommandes();

        Commande selectedCommande = null;
        for (Commande commande : commandes) {
            if (commande.getId() == commandeId) {
                selectedCommande = commande;
                break;
            }
        }

        if (selectedCommande == null) {
            JOptionPane.showMessageDialog(this, "Commande introuvable", "Erreur", JOptionPane.ERROR_MESSAGE);
            return;
        }

        // Créer une copie finale de la commande sélectionnée pour utilisation dans les lambdas
        final Commande finalSelectedCommande = selectedCommande;

        // Créer une fenêtre modale pour afficher les détails
        JDialog detailsDialog = new JDialog(this, "Détails de la commande #" + commandeId, true);
        detailsDialog.setSize(500, 400);
        detailsDialog.setLocationRelativeTo(this);
        detailsDialog.setLayout(new BorderLayout());

        // Panel d'informations générales
        JPanel infoPanel = new JPanel(new GridLayout(3, 2, 10, 5));
        infoPanel.setBorder(BorderFactory.createEmptyBorder(10, 10, 10, 10));

        SimpleDateFormat dateFormat = new SimpleDateFormat("dd/MM/yyyy HH:mm");

        infoPanel.add(new JLabel("Numéro de table:"));
        infoPanel.add(new JLabel(String.valueOf(finalSelectedCommande.getTableNumber())));

        infoPanel.add(new JLabel("Date:"));
        infoPanel.add(new JLabel(dateFormat.format(finalSelectedCommande.getDateCommande())));

        infoPanel.add(new JLabel("Total:"));
        infoPanel.add(new JLabel(String.format("%.2f€", finalSelectedCommande.getTotalCommande())));

        detailsDialog.add(infoPanel, BorderLayout.NORTH);

        // Liste des plats
        DefaultTableModel platsModel = new DefaultTableModel(
                new String[]{"Plat", "Prix"}, 0);

        for (Plat plat : finalSelectedCommande.getMenus()) {
            platsModel.addRow(new Object[]{
                plat.getName(),
                String.format("%.2f€", plat.getPrice())
            });
        }

        JTable platsTable = new JTable(platsModel);
        platsTable.setRowHeight(25);

        JScrollPane platsScrollPane = new JScrollPane(platsTable);
        platsScrollPane.setBorder(BorderFactory.createTitledBorder("Plats commandés"));

        detailsDialog.add(platsScrollPane, BorderLayout.CENTER);

        // Panel de boutons
        JPanel buttonPanel = new JPanel();

        JCheckBox paidCheckBox = new JCheckBox("Commande payée", finalSelectedCommande.isPayee());
        buttonPanel.add(paidCheckBox);

        JButton saveButton = new JButton("Enregistrer");
        saveButton.addActionListener(e -> {
            boolean newPaidStatus = paidCheckBox.isSelected();
            if (newPaidStatus != finalSelectedCommande.isPayee()) {
                boolean success = commandeDAO.updateCommandePayee(commandeId, newPaidStatus);
                if (success) {
                    JOptionPane.showMessageDialog(detailsDialog,
                            "Statut de paiement mis à jour avec succès",
                            "Succès", JOptionPane.INFORMATION_MESSAGE);
                    refreshTableData();
                    detailsDialog.dispose();
                } else {
                    JOptionPane.showMessageDialog(detailsDialog,
                            "Erreur lors de la mise à jour du statut de paiement",
                            "Erreur", JOptionPane.ERROR_MESSAGE);
                }
            } else {
                detailsDialog.dispose();
            }
        });
        buttonPanel.add(saveButton);

        JButton cancelButton = new JButton("Annuler");
        cancelButton.addActionListener(e -> detailsDialog.dispose());
        buttonPanel.add(cancelButton);

        detailsDialog.add(buttonPanel, BorderLayout.SOUTH);

        detailsDialog.setVisible(true);
    }



    public void showUI() {
        refreshTableData();
        setVisible(true);
    }
}
