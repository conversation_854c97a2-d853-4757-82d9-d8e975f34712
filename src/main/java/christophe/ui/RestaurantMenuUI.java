
package christophe.ui;

import javax.swing.*;
import java.awt.*;
import java.awt.event.ActionEvent;
import java.awt.event.ActionListener;
import java.util.List;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.Map;
import christophe.Carte.Carte;
import christophe.Carte.Plat;
import christophe.commande.Commande;
import christophe.DAO.CommandeDAO;

// ajouter une barre de sous menu avec les commande du jours pour la DAO. liste des commandes et listes des commandes du jours.

public class RestaurantMenuUI extends JFrame {
    private Carte carte;
    private JSpinner tableSpinner;
    private Map<Integer, List<Commande>> tableOrders; // Liste de menus par table
    private Map<Integer, Commande> finishedOrders; // Commandes terminées mais pas encore exportées
    private Map<String, JComboBox<String>> sectionComboBoxes;
    private JTextArea orderDisplay;
    private JButton btnAddMenu, btnCompleteOrder, btnExportPDF;
    private CommandeDAO commandeDAO; // Ajout de cette ligne

    public RestaurantMenuUI(Carte carte) {
        this.carte = carte;
        this.tableOrders = new HashMap<>();
        this.finishedOrders = new HashMap<>();
        this.sectionComboBoxes = new HashMap<>();

        try {
            this.commandeDAO = new CommandeDAO(); // Initialisation de la DAO
        } catch (Exception e) {
            System.err.println("Erreur lors de l'initialisation de la DAO: " + e.getMessage());
            e.printStackTrace();
            JOptionPane.showMessageDialog(this,
                "Erreur lors de l'initialisation de la base de données.\n" +
                "L'application fonctionnera en mode dégradé (sans sauvegarde permanente).",
                "Erreur", JOptionPane.ERROR_MESSAGE);
        }

        initUI();
    }

    private void initUI() {
        setTitle("Restaurant Menu");
        setDefaultCloseOperation(EXIT_ON_CLOSE);
        setSize(900, 700);
        setLocationRelativeTo(null);
        setLayout(new BorderLayout());

        // Ajout de la barre de menu
        JMenuBar menuBar = new JMenuBar();
        JMenu rapportsMenu = new JMenu("Rapports");

        JMenuItem ventesJourItem = new JMenuItem("Ventes du jour");
        ventesJourItem.addActionListener(e -> afficherVentesDuJour());

        JMenuItem listeCommandesItem = new JMenuItem("Liste des commandes");
        listeCommandesItem.addActionListener(e -> afficherListeCommandes());

        JMenuItem statsVentesItem = new JMenuItem("Statistiques des ventes");
        statsVentesItem.addActionListener(e -> afficherStatistiquesVentes());

        JMenuItem statsCommandesItem = new JMenuItem("Statistiques des commandes");
        statsCommandesItem.addActionListener(e -> afficherStatistiquesCommandes());

        rapportsMenu.add(ventesJourItem);
        rapportsMenu.add(listeCommandesItem);
        rapportsMenu.add(statsVentesItem);
        rapportsMenu.add(statsCommandesItem);
        menuBar.add(rapportsMenu);
        setJMenuBar(menuBar);

        JPanel mainPanel = new JPanel();
        mainPanel.setLayout(new BoxLayout(mainPanel, BoxLayout.Y_AXIS));

        JPanel tablePanel = new JPanel(new FlowLayout(FlowLayout.LEFT));
        tablePanel.setBorder(BorderFactory.createTitledBorder("Sélection de la table"));
        tablePanel.add(new JLabel("Numéro de table:"));
        tableSpinner = new JSpinner(new SpinnerNumberModel(1, 1, 100, 1));
        tableSpinner.addChangeListener(e -> updateOrderDisplay());
        tablePanel.add(tableSpinner);
        mainPanel.add(tablePanel);

        for (String section : carte.getMenuItems().keySet()) {
            JPanel sectionPanel = new JPanel();
            sectionPanel.setLayout(new BoxLayout(sectionPanel, BoxLayout.Y_AXIS));
            sectionPanel.setBorder(BorderFactory.createTitledBorder("Sélection de " + section));

            Map<String, List<Plat>> subCategories = carte.getMenuItems().get(section);
            ButtonGroup group = new ButtonGroup();
            JPanel radioPanel = new JPanel(new FlowLayout(FlowLayout.LEFT));

            for (String subCategory : subCategories.keySet()) {
                JRadioButton radioButton = new JRadioButton(subCategory);
                group.add(radioButton);
                radioPanel.add(radioButton);

                radioButton.addActionListener(e -> updateComboBox(section, subCategory));
            }

            sectionPanel.add(radioPanel);

            JComboBox<String> comboBox = new JComboBox<>();
            sectionComboBoxes.put(section, comboBox);
            sectionPanel.add(comboBox);

            mainPanel.add(sectionPanel);
        }

        JPanel orderPanel = new JPanel(new BorderLayout());
        orderPanel.setBorder(BorderFactory.createTitledBorder("Commande en cours"));
        orderDisplay = new JTextArea(15, 30);
        orderDisplay.setEditable(false);
        orderPanel.add(new JScrollPane(orderDisplay), BorderLayout.CENTER);
        mainPanel.add(orderPanel);

        JPanel buttonPanel = new JPanel(new FlowLayout(FlowLayout.CENTER));
        btnAddMenu = new JButton("Ajouter Menu");
        btnCompleteOrder = new JButton("Terminer Commande");
        btnExportPDF = new JButton("Exporter vers DB");
        btnExportPDF.setEnabled(false);

        btnAddMenu.addActionListener(e -> addMenu());
        btnCompleteOrder.addActionListener(e -> completeOrder());
        btnExportPDF.addActionListener(e -> exportOrderToDB());

        buttonPanel.add(btnAddMenu);
        buttonPanel.add(btnCompleteOrder);
//        buttonPanel.add(btnExportPDF);  // n'est plus utile
        mainPanel.add(buttonPanel);

        add(mainPanel, BorderLayout.CENTER);
    }

    private void updateComboBox(String section, String subCategory) {
        JComboBox<String> comboBox = sectionComboBoxes.get(section);
        if (comboBox == null) return;

        comboBox.removeAllItems();
        List<Plat> options = carte.getMenuItems().get(section).get(subCategory);

        for (Plat option : options) {
            comboBox.addItem(option.getName() + " - " + option.getPrice() + "€");
        }
    }

    private void addMenu() {
        int tableNumber = (int) tableSpinner.getValue();
        tableOrders.putIfAbsent(tableNumber, new ArrayList<>());

        Commande newMenu = new Commande();
        newMenu.setTableNumber(tableNumber);  // Ajout de cette ligne

        for (String section : sectionComboBoxes.keySet()) {
            JComboBox<String> comboBox = sectionComboBoxes.get(section);
            if (comboBox.getSelectedItem() != null) {
                String selectedItem = comboBox.getSelectedItem().toString().split(" - ")[0];

                for (List<Plat> plats : carte.getMenuItems().get(section).values()) {
                    for (Plat plat : plats) {
                        if (plat.getName().equals(selectedItem)) {
                            newMenu.ajouterMenu(plat);
                        }
                    }
                }
            }
        }

        if (!newMenu.getMenus().isEmpty()) {
            tableOrders.get(tableNumber).add(newMenu);
        }

        updateOrderDisplay();
    }

    private void completeOrder() {
        int tableNumber = (int) tableSpinner.getValue();
        List<Commande> commandes = tableOrders.get(tableNumber);

        if (commandes != null && !commandes.isEmpty()) {
            finishedOrders.put(tableNumber, new Commande());
            finishedOrders.get(tableNumber).setTableNumber(tableNumber);

            for (Commande menu : commandes) {
                for (Plat plat : menu.getMenus()) {
                    finishedOrders.get(tableNumber).ajouterMenu(plat);
                }
            }

            // Sauvegarder la commande dans la base de données
            boolean success = commandeDAO.saveCommande(finishedOrders.get(tableNumber));

            if (!success) {
                JOptionPane.showMessageDialog(this, "Erreur lors de l'enregistrement de la commande dans la base de données.",
                        "Erreur", JOptionPane.ERROR_MESSAGE);
            }

            JOptionPane.showMessageDialog(this, "Commande terminée pour la table " + tableNumber +
                    ". Total: " + finishedOrders.get(tableNumber).getTotalCommande() + "€", "Message", JOptionPane.INFORMATION_MESSAGE);
            btnExportPDF.setEnabled(true);

            tableOrders.put(tableNumber, new ArrayList<>()); // Reset uniquement la commande en cours
        } else {
            JOptionPane.showMessageDialog(this, "Aucune commande enregistrée pour cette table.");
        }

        updateOrderDisplay();
    }

    private void exportOrderToDB() {
        int tableNumber = (Integer) tableSpinner.getValue();
        Commande finishedCommande = finishedOrders.get(tableNumber);

        if (finishedCommande != null && !finishedCommande.getMenus().isEmpty()) {
            // Sauvegarder la commande dans la base de données
            boolean success = commandeDAO.saveCommande(finishedCommande);

            if (success) {
                JOptionPane.showMessageDialog(this, "Commande exportée vers la base de données avec succès.");
                btnExportPDF.setEnabled(false);
                finishedOrders.remove(tableNumber); // On vide uniquement les commandes exportées
            } else {
                JOptionPane.showMessageDialog(this, "Erreur lors de l'exportation vers la base de données.",
                        "Erreur", JOptionPane.ERROR_MESSAGE);
            }
        } else {
            JOptionPane.showMessageDialog(this, "Aucune commande terminée pour cette table.");
        }
    }

    private void updateOrderDisplay() {
        int tableNumber = (int) tableSpinner.getValue();
        List<Commande> commandes = tableOrders.getOrDefault(tableNumber, new ArrayList<>());

        StringBuilder displayText = new StringBuilder("Table " + tableNumber + "\n");

        int count = 1;
        for (Commande menu : commandes) {
            displayText.append("Menu ").append(count++).append("\n");
            for (Plat plat : menu.getMenus()) {
                displayText.append("  - ").append(plat.getName())
                        .append(" - Prix: ").append(plat.getPrice()).append("€\n");
            }
            displayText.append("\n");
        }

        double total = commandes.stream().mapToDouble(Commande::getTotalCommande).sum();
        displayText.append("Total de la commande: ").append(total).append("€\n=== Commande en cours ===");

        orderDisplay.setText(displayText.toString());
    }

    // Méthode pour afficher les ventes du jour
    private void afficherVentesDuJour() {
        JFrame ventesFrame = new JFrame("Ventes du jour");
        ventesFrame.setSize(800, 600);
        ventesFrame.setLocationRelativeTo(this);

        JPanel panel = new JPanel(new BorderLayout());
        JTextArea textArea = new JTextArea();
        textArea.setEditable(false);
        textArea.setFont(new Font(Font.MONOSPACED, Font.PLAIN, 12));

        // Récupérer les ventes du jour depuis la base de données
        List<Commande> commandesDuJour = commandeDAO.getCommandesDuJour();

        StringBuilder rapport = new StringBuilder();
        rapport.append("=== RAPPORT DES VENTES DU JOUR ===\n\n");

        double totalJour = 0;
        int numeroCommande = 1;

        for (Commande commande : commandesDuJour) {
            rapport.append("Commande #").append(numeroCommande).append(" - Table ").append(commande.getTableNumber());
            rapport.append(" - ").append(commande.isPayee() ? "PAYÉE" : "NON PAYÉE").append("\n");
            rapport.append("Date: ").append(new java.text.SimpleDateFormat("dd/MM/yyyy HH:mm").format(commande.getDateCommande())).append("\n");
            rapport.append("Détail de la commande:\n");

            // Afficher le détail des plats
            for (Plat plat : commande.getMenus()) {
                rapport.append("  - ").append(plat.getName()).append(" : ").append(String.format("%.2f€", plat.getPrice())).append("\n");
            }

            rapport.append("Total commande: ").append(String.format("%.2f€", commande.getTotalCommande())).append("\n");
            rapport.append("----------------------------------------\n\n");

            totalJour += commande.getTotalCommande();
            numeroCommande++;
        }

        rapport.append("=== RÉSUMÉ ===\n");
        rapport.append("Nombre de commandes: ").append(commandesDuJour.size()).append("\n");
        rapport.append("Total du jour: ").append(String.format("%.2f€", totalJour)).append("\n");

        textArea.setText(rapport.toString());

        panel.add(new JScrollPane(textArea), BorderLayout.CENTER);

        JButton closeButton = new JButton("Fermer");
        closeButton.addActionListener(e -> ventesFrame.dispose());

        JPanel buttonPanel = new JPanel();
        buttonPanel.add(closeButton);
        panel.add(buttonPanel, BorderLayout.SOUTH);

        ventesFrame.add(panel);
        ventesFrame.setVisible(true);
    }

    // Méthode pour afficher la liste des commandes
    private void afficherListeCommandes() {
        CommandesListUI commandesListUI = new CommandesListUI();
        commandesListUI.showUI();
    }

    // Méthode pour afficher les statistiques des ventes
    private void afficherStatistiquesVentes() {
        StatistiquesUI statsUI = new StatistiquesUI("ventes");
        statsUI.showUI();
    }

    // Méthode pour afficher les statistiques des commandes
    private void afficherStatistiquesCommandes() {
        StatistiquesUI statsUI = new StatistiquesUI("commandes");
        statsUI.showUI();
    }
}
