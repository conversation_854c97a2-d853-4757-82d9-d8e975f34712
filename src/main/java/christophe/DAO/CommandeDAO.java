package christophe.DAO;

import christophe.commande.Commande;
import christophe.Carte.Plat;
import org.w3c.dom.Document;
import org.w3c.dom.Element;
import org.w3c.dom.NodeList;

import javax.xml.parsers.DocumentBuilder;
import javax.xml.parsers.DocumentBuilderFactory;
import java.io.File;
import java.sql.*;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Date;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import javax.swing.JOptionPane;

public class CommandeDAO {
    private String dbUrl;
    private String user;
    private String password;

    private boolean driverLoaded = false;
    private boolean dbAvailable = false;

    public CommandeDAO() {
        // Charger explicitement le pilote MySQL
        try {
            Class.forName("com.mysql.cj.jdbc.Driver");
            System.out.println("Pilote MySQL chargé avec succès");
            driverLoaded = true;
        } catch (ClassNotFoundException e) {
            System.err.println("Erreur lors du chargement du pilote MySQL: " + e.getMessage());
            System.err.println("L'application fonctionnera en mode dégradé (sans base de données)");
            JOptionPane.showMessageDialog(null,
                "Le pilote MySQL n'a pas été trouvé. L'application fonctionnera sans base de données.\n" +
                "Les commandes ne seront pas sauvegardées de façon permanente.",
                "Avertissement", JOptionPane.WARNING_MESSAGE);
            e.printStackTrace();
        }

        if (driverLoaded) {
            loadConfig();
            dbAvailable = initDatabase();
        }
    }

    private void loadConfig() {
        try {
            File configFile = new File("src/main/resources/db_config.xml");

            if (!configFile.exists()) {
                System.err.println("ERREUR: Fichier de configuration introuvable: " + configFile.getAbsolutePath());
                return;
            }

            DocumentBuilderFactory dbFactory = DocumentBuilderFactory.newInstance();
            DocumentBuilder dBuilder = dbFactory.newDocumentBuilder();
            Document doc = dBuilder.parse(configFile);
            doc.getDocumentElement().normalize();

            NodeList userNode = doc.getElementsByTagName("user");
            NodeList passwordNode = doc.getElementsByTagName("password");
            NodeList portNode = doc.getElementsByTagName("port");
            NodeList dbNameNode = doc.getElementsByTagName("dbname");
            NodeList hostNode = doc.getElementsByTagName("host");

            if (userNode.getLength() == 0 || passwordNode.getLength() == 0 ||
                portNode.getLength() == 0 || dbNameNode.getLength() == 0 ||
                hostNode.getLength() == 0) {
                System.err.println("ERREUR: Configuration de base de données incomplète dans le fichier XML");
                return;
            }

            String host = hostNode.item(0).getTextContent();
            String port = portNode.item(0).getTextContent();
            String dbName = dbNameNode.item(0).getTextContent();

            this.user = userNode.item(0).getTextContent();
            this.password = passwordNode.item(0).getTextContent();
            this.dbUrl = "jdbc:mysql://" + host + ":" + port + "/" + dbName;

            System.out.println("Configuration de la base de données chargée avec succès");
            System.out.println("URL de connexion: " + this.dbUrl);
        } catch (Exception e) {
            System.err.println("ERREUR lors du chargement de la configuration: " + e.getMessage());
            e.printStackTrace();
        }
    }

    private boolean initDatabase() {
        if (dbUrl == null || user == null || password == null) {
            System.err.println("ERREUR: Configuration de base de données incomplète. Impossible d'initialiser la base de données.");
            return false;
        }

        // Extraire le nom de la base de données de l'URL
        String dbName = extractDatabaseName(dbUrl);
        if (dbName == null) {
            System.err.println("ERREUR: Impossible d'extraire le nom de la base de données de l'URL: " + dbUrl);
            return false;
        }

        // URL de connexion sans le nom de la base de données (pour se connecter au serveur MySQL)
        String serverUrl = dbUrl.substring(0, dbUrl.lastIndexOf('/'));

        try {
            // 1. Vérifier si la base de données existe, sinon la créer
            System.out.println("Tentative de connexion au serveur MySQL: " + serverUrl);
            Connection serverConn = DriverManager.getConnection(serverUrl, user, password);
            System.out.println("Connexion au serveur MySQL établie avec succès");

            // Vérifier si la base de données existe
            ResultSet databases = serverConn.getMetaData().getCatalogs();
            boolean dbExists = false;
            while (databases.next()) {
                String existingDbName = databases.getString(1);
                if (existingDbName.equalsIgnoreCase(dbName)) {
                    dbExists = true;
                    System.out.println("Base de données '" + dbName + "' existe déjà");
                    break;
                }
            }

            // Créer la base de données si elle n'existe pas
            if (!dbExists) {
                System.out.println("Base de données '" + dbName + "' n'existe pas, création...");
                Statement stmt = serverConn.createStatement();
                stmt.executeUpdate("CREATE DATABASE " + dbName);
                System.out.println("Base de données '" + dbName + "' créée avec succès");
            }

            serverConn.close();

            // 2. Se connecter à la base de données et vérifier/créer les tables
            System.out.println("Tentative de connexion à la base de données: " + dbUrl);
            Connection conn = DriverManager.getConnection(dbUrl, user, password);
            System.out.println("Connexion établie avec succès");

            // Créer les tables si elles n'existent pas
            createTablesIfNotExist(conn);

            conn.close();
            System.out.println("Initialisation de la base de données terminée");
            return true;

        } catch (SQLException e) {
            System.err.println("ERREUR SQL lors de l'initialisation de la base de données: " + e.getMessage());
            e.printStackTrace();
            return false;
        }
    }

    private String extractDatabaseName(String dbUrl) {
        // Format attendu: *******************************************
        int lastSlashIndex = dbUrl.lastIndexOf('/');
        if (lastSlashIndex != -1 && lastSlashIndex < dbUrl.length() - 1) {
            return dbUrl.substring(lastSlashIndex + 1);
        }
        return null;
    }

    private void createTablesIfNotExist(Connection conn) throws SQLException {
        DatabaseMetaData metaData = conn.getMetaData();

        // Vérifier si la table commandes existe
        ResultSet tables = metaData.getTables(null, null, "commandes", null);
        boolean commandesTableExists = tables.next();

        if (!commandesTableExists) {
            System.out.println("Table 'commandes' n'existe pas, création...");
            Statement stmt = conn.createStatement();

            // Table des commandes
            stmt.execute("CREATE TABLE commandes (" +
                    "id INT AUTO_INCREMENT PRIMARY KEY, " +
                    "table_number INT NOT NULL, " +
                    "date_commande DATETIME NOT NULL, " +
                    "total DECIMAL(10,2) NOT NULL, " +
                    "payee BOOLEAN DEFAULT FALSE)");
            System.out.println("Table 'commandes' créée avec succès");
        } else {
            System.out.println("Table 'commandes' existe déjà");
            // Afficher la structure de la table
            ResultSet columns = metaData.getColumns(null, null, "commandes", null);
            System.out.println("Structure de la table 'commandes':");
            while (columns.next()) {
                String columnName = columns.getString("COLUMN_NAME");
                String columnType = columns.getString("TYPE_NAME");
                System.out.println("  - " + columnName + " " + columnType);
            }
        }

        // Vérifier si la table commande_plats existe
        tables = metaData.getTables(null, null, "commande_plats", null);
        boolean commandePlatsTableExists = tables.next();

        if (!commandePlatsTableExists) {
            System.out.println("Table 'commande_plats' n'existe pas, création...");
            Statement stmt = conn.createStatement();

            // Table des plats dans les commandes
            stmt.execute("CREATE TABLE commande_plats (" +
                    "id INT AUTO_INCREMENT PRIMARY KEY, " +
                    "commande_id INT NOT NULL, " +
                    "nom_plat VARCHAR(255) NOT NULL, " +
                    "prix DECIMAL(10,2) NOT NULL, " +
                    "FOREIGN KEY (commande_id) REFERENCES commandes(id))");
            System.out.println("Table 'commande_plats' créée avec succès");
        } else {
            System.out.println("Table 'commande_plats' existe déjà");
            // Afficher la structure de la table
            ResultSet columns = metaData.getColumns(null, null, "commande_plats", null);
            System.out.println("Structure de la table 'commande_plats':");
            while (columns.next()) {
                String columnName = columns.getString("COLUMN_NAME");
                String columnType = columns.getString("TYPE_NAME");
                System.out.println("  - " + columnName + " " + columnType);
            }
        }
    }

    public boolean saveCommande(Commande commande) {
        if (!dbAvailable) {
            System.err.println("Base de données non disponible. La commande ne sera pas sauvegardée.");
            // Simuler un succès pour ne pas bloquer l'application
            return true;
        }

        if (commande == null) {
            System.err.println("ERREUR: Commande null. Impossible de sauvegarder.");
            return false;
        }

        try {
            System.out.println("Tentative de connexion à la base de données pour sauvegarder une commande");
            Connection conn = DriverManager.getConnection(dbUrl, user, password);
            System.out.println("Connexion établie avec succès");

            // Désactiver l'auto-commit pour la transaction
            conn.setAutoCommit(false);

            try {
                // Insérer la commande
                PreparedStatement pstmt = conn.prepareStatement(
                        "INSERT INTO commandes (table_number, date_commande, total, payee) VALUES (?, ?, ?, ?)",
                        Statement.RETURN_GENERATED_KEYS);

                pstmt.setInt(1, commande.getTableNumber());
                pstmt.setTimestamp(2, Timestamp.valueOf(LocalDateTime.now()));
                pstmt.setDouble(3, commande.getTotalCommande());
                pstmt.setBoolean(4, commande.isPayee()); // Utiliser le statut de paiement de la commande

                pstmt.executeUpdate();
                System.out.println("Commande insérée dans la table 'commandes'");

                // Récupérer l'ID généré
                ResultSet generatedKeys = pstmt.getGeneratedKeys();
                int commandeId = -1;
                if (generatedKeys.next()) {
                    commandeId = generatedKeys.getInt(1);
                    System.out.println("ID de commande généré: " + commandeId);
                }

                // Insérer les plats de la commande
                if (commandeId != -1) {
                    PreparedStatement pstmtPlats = conn.prepareStatement(
                            "INSERT INTO commande_plats (commande_id, nom_plat, prix) VALUES (?, ?, ?)");

                    int platCount = 0;
                    for (Plat plat : commande.getMenus()) {
                        pstmtPlats.setInt(1, commandeId);
                        pstmtPlats.setString(2, plat.getName());
                        pstmtPlats.setDouble(3, plat.getPrice());
                        pstmtPlats.executeUpdate();
                        platCount++;
                    }
                    System.out.println(platCount + " plats insérés dans la table 'commande_plats'");
                }

                // Valider la transaction
                conn.commit();
                System.out.println("Transaction validée avec succès");
                conn.close();
                return true; // Succès
            } catch (SQLException e) {
                // En cas d'erreur, annuler la transaction
                conn.rollback();
                System.err.println("ERREUR SQL lors de la sauvegarde de la commande: " + e.getMessage());
                e.printStackTrace();
                conn.close();
                return false; // Échec
            }
        } catch (SQLException e) {
            System.err.println("ERREUR de connexion lors de la sauvegarde de la commande: " + e.getMessage());
            e.printStackTrace();
            return false; // Échec
        }
    }

    public List<Commande> getCommandesDuJour() {
        List<Commande> commandes = new ArrayList<>();

        if (!dbAvailable) {
            System.err.println("Base de données non disponible. Aucune commande ne sera récupérée.");
            return commandes;
        }

        try {
            Connection conn = DriverManager.getConnection(dbUrl, user, password);
            System.out.println("Connexion établie pour récupérer les commandes du jour");

            // D'abord, vérifions les noms des colonnes dans la table commandes
            DatabaseMetaData metaData = conn.getMetaData();
            ResultSet columns = metaData.getColumns(null, null, "commandes", null);

            // Stockage des noms de colonnes pour vérification
            List<String> columnNames = new ArrayList<>();
            while (columns.next()) {
                columnNames.add(columns.getString("COLUMN_NAME").toLowerCase());
            }

            System.out.println("Colonnes disponibles dans la table commandes: " + columnNames);

            // Noms de colonnes attendus et leurs alternatives possibles
            String idColumn = "id";
            String tableNumberColumn = columnNames.contains("table_number") ? "table_number" :
                                      columnNames.contains("tablenumber") ? "tablenumber" :
                                      columnNames.contains("table") ? "table" : "id"; // Fallback à id si aucune colonne trouvée

            String totalColumn = columnNames.contains("total") ? "total" :
                               columnNames.contains("montant") ? "montant" :
                               columnNames.contains("prix") ? "prix" : "id"; // Fallback à id

            String payeeColumn = columnNames.contains("payee") ? "payee" :
                               columnNames.contains("paye") ? "paye" :
                               columnNames.contains("paid") ? "paid" : "id"; // Fallback à id

            String dateColumn = columnNames.contains("date_commande") ? "date_commande" :
                              columnNames.contains("datecommande") ? "datecommande" :
                              columnNames.contains("date") ? "date" : "id"; // Fallback à id

            System.out.println("Utilisation des colonnes: id=" + idColumn + ", table=" + tableNumberColumn +
                             ", total=" + totalColumn + ", payee=" + payeeColumn + ", date=" + dateColumn);

            // Adapter la requête SQL en fonction de la colonne de date disponible
            String sql = "SELECT * FROM commandes";
            if (!dateColumn.equals("id")) {
                sql = "SELECT * FROM commandes WHERE DATE(" + dateColumn + ") = CURDATE()";
            }

            // Afficher des informations de débogage sur les dates
            if (!dateColumn.equals("id")) {
                PreparedStatement debugStmt = conn.prepareStatement(
                    "SELECT CURDATE() as date_actuelle, " +
                    "COUNT(*) as total_commandes, " +
                    "MIN(DATE(" + dateColumn + ")) as premiere_date, " +
                    "MAX(DATE(" + dateColumn + ")) as derniere_date " +
                    "FROM commandes");
                ResultSet debugRs = debugStmt.executeQuery();
                if (debugRs.next()) {
                    System.out.println("=== INFORMATIONS DE DÉBOGAGE ===");
                    System.out.println("Date actuelle du serveur MySQL: " + debugRs.getString("date_actuelle"));
                    System.out.println("Nombre total de commandes: " + debugRs.getInt("total_commandes"));
                    System.out.println("Première date de commande: " + debugRs.getString("premiere_date"));
                    System.out.println("Dernière date de commande: " + debugRs.getString("derniere_date"));
                    System.out.println("=================================");
                }

                // Afficher toutes les dates distinctes pour le débogage
                PreparedStatement datesStmt = conn.prepareStatement(
                    "SELECT DISTINCT DATE(" + dateColumn + ") as date_commande, COUNT(*) as nb_commandes " +
                    "FROM commandes GROUP BY DATE(" + dateColumn + ") ORDER BY date_commande DESC LIMIT 10");
                ResultSet datesRs = datesStmt.executeQuery();
                System.out.println("Dates de commandes disponibles (10 dernières):");
                while (datesRs.next()) {
                    System.out.println("  - " + datesRs.getString("date_commande") + " (" + datesRs.getInt("nb_commandes") + " commandes)");
                }
            }

            PreparedStatement pstmt = conn.prepareStatement(sql);

            ResultSet rs = pstmt.executeQuery();
            System.out.println("Requête exécutée pour les commandes du jour: " + sql);

            int count = 0;
            while (rs.next()) {
                try {
                    int id = rs.getInt(idColumn);

                    // Utiliser des valeurs par défaut si les colonnes n'existent pas
                    int tableNumber = 0;
                    try {
                        tableNumber = rs.getInt(tableNumberColumn);
                    } catch (SQLException e) {
                        System.out.println("Colonne " + tableNumberColumn + " non trouvée, utilisation de la valeur par défaut 0");
                    }

                    double total = 0.0;
                    try {
                        total = rs.getDouble(totalColumn);
                    } catch (SQLException e) {
                        System.out.println("Colonne " + totalColumn + " non trouvée, utilisation de la valeur par défaut 0.0");
                    }

                    boolean payee = false;
                    try {
                        payee = rs.getBoolean(payeeColumn);
                    } catch (SQLException e) {
                        System.out.println("Colonne " + payeeColumn + " non trouvée, utilisation de la valeur par défaut false");
                    }

                    java.util.Date dateCommande = new java.util.Date();
                    try {
                        Timestamp timestamp = rs.getTimestamp(dateColumn);
                        if (timestamp != null) {
                            dateCommande = new java.util.Date(timestamp.getTime());
                        }
                    } catch (SQLException e) {
                        System.out.println("Colonne " + dateColumn + " non trouvée, utilisation de la date actuelle");
                    }

                    Commande commande = new Commande();
                    commande.setId(id);
                    commande.setTableNumber(tableNumber);
                    commande.setPayee(payee);
                    commande.setDateCommande(dateCommande);

                    // Récupérer les plats de cette commande si la table existe
                    try {
                        PreparedStatement pstmtPlats = conn.prepareStatement(
                                "SELECT * FROM commande_plats WHERE commande_id = ?");
                        pstmtPlats.setInt(1, id);

                        ResultSet rsPlats = pstmtPlats.executeQuery();

                        int platCount = 0;
                        while (rsPlats.next()) {
                            String nomPlat = "Plat inconnu";
                            double prix = 0.0;

                            try {
                                nomPlat = rsPlats.getString("nom_plat");
                            } catch (SQLException e) {
                                System.out.println("Colonne nom_plat non trouvée, utilisation de la valeur par défaut");
                            }

                            try {
                                prix = rsPlats.getDouble("prix");
                            } catch (SQLException e) {
                                System.out.println("Colonne prix non trouvée, utilisation de la valeur par défaut");
                            }

                            Plat plat = new Plat(nomPlat, prix, "");
                            commande.ajouterMenu(plat);
                            platCount++;
                        }

                        System.out.println("Commande #" + id + " récupérée avec " + platCount + " plats");
                    } catch (SQLException e) {
                        System.out.println("Erreur lors de la récupération des plats pour la commande #" + id + ": " + e.getMessage());
                    }

                    commandes.add(commande);
                    count++;
                } catch (SQLException e) {
                    System.out.println("Erreur lors de la récupération de la commande: " + e.getMessage());
                }
            }

            System.out.println(count + " commandes du jour récupérées");
            conn.close();

        } catch (SQLException e) {
            System.err.println("ERREUR SQL lors de la récupération des commandes du jour: " + e.getMessage());
            e.printStackTrace();
        }

        return commandes;
    }

    public List<Commande> getToutesCommandes() {
        // Similaire à getCommandesDuJour mais sans le filtre de date
        List<Commande> commandes = new ArrayList<>();

        if (!dbAvailable) {
            System.err.println("Base de données non disponible. Aucune commande ne sera récupérée.");
            return commandes;
        }

        try {
            Connection conn = DriverManager.getConnection(dbUrl, user, password);
            System.out.println("Connexion établie pour récupérer toutes les commandes");

            // D'abord, vérifions les noms des colonnes dans la table commandes
            DatabaseMetaData metaData = conn.getMetaData();
            ResultSet columns = metaData.getColumns(null, null, "commandes", null);

            // Stockage des noms de colonnes pour vérification
            List<String> columnNames = new ArrayList<>();
            while (columns.next()) {
                columnNames.add(columns.getString("COLUMN_NAME").toLowerCase());
            }

            System.out.println("Colonnes disponibles dans la table commandes: " + columnNames);

            // Noms de colonnes attendus et leurs alternatives possibles
            String idColumn = "id";
            String tableNumberColumn = columnNames.contains("table_number") ? "table_number" :
                                      columnNames.contains("tablenumber") ? "tablenumber" :
                                      columnNames.contains("table") ? "table" : "id"; // Fallback à id si aucune colonne trouvée

            String totalColumn = columnNames.contains("total") ? "total" :
                               columnNames.contains("montant") ? "montant" :
                               columnNames.contains("prix") ? "prix" : "id"; // Fallback à id

            String payeeColumn = columnNames.contains("payee") ? "payee" :
                               columnNames.contains("paye") ? "paye" :
                               columnNames.contains("paid") ? "paid" : "id"; // Fallback à id

            String dateColumn = columnNames.contains("date_commande") ? "date_commande" :
                              columnNames.contains("datecommande") ? "datecommande" :
                              columnNames.contains("date") ? "date" : "id"; // Fallback à id

            System.out.println("Utilisation des colonnes: id=" + idColumn + ", table=" + tableNumberColumn +
                             ", total=" + totalColumn + ", payee=" + payeeColumn + ", date=" + dateColumn);

            PreparedStatement pstmt = conn.prepareStatement(
                    "SELECT * FROM commandes");

            ResultSet rs = pstmt.executeQuery();
            System.out.println("Requête exécutée pour toutes les commandes");

            int count = 0;
            while (rs.next()) {
                try {
                    int id = rs.getInt(idColumn);

                    // Utiliser des valeurs par défaut si les colonnes n'existent pas
                    int tableNumber = 0;
                    try {
                        tableNumber = rs.getInt(tableNumberColumn);
                    } catch (SQLException e) {
                        System.out.println("Colonne " + tableNumberColumn + " non trouvée, utilisation de la valeur par défaut 0");
                    }

                    double total = 0.0;
                    try {
                        total = rs.getDouble(totalColumn);
                    } catch (SQLException e) {
                        System.out.println("Colonne " + totalColumn + " non trouvée, utilisation de la valeur par défaut 0.0");
                    }

                    boolean payee = false;
                    try {
                        payee = rs.getBoolean(payeeColumn);
                    } catch (SQLException e) {
                        System.out.println("Colonne " + payeeColumn + " non trouvée, utilisation de la valeur par défaut false");
                    }

                    java.util.Date dateCommande = new java.util.Date();
                    try {
                        Timestamp timestamp = rs.getTimestamp(dateColumn);
                        if (timestamp != null) {
                            dateCommande = new java.util.Date(timestamp.getTime());
                        }
                    } catch (SQLException e) {
                        System.out.println("Colonne " + dateColumn + " non trouvée, utilisation de la date actuelle");
                    }

                    Commande commande = new Commande();
                    commande.setId(id);
                    commande.setTableNumber(tableNumber);
                    commande.setPayee(payee);
                    commande.setDateCommande(dateCommande);

                    // Récupérer les plats de cette commande si la table existe
                    try {
                        PreparedStatement pstmtPlats = conn.prepareStatement(
                                "SELECT * FROM commande_plats WHERE commande_id = ?");
                        pstmtPlats.setInt(1, id);

                        ResultSet rsPlats = pstmtPlats.executeQuery();

                        int platCount = 0;
                        while (rsPlats.next()) {
                            String nomPlat = "Plat inconnu";
                            double prix = 0.0;

                            try {
                                nomPlat = rsPlats.getString("nom_plat");
                            } catch (SQLException e) {
                                System.out.println("Colonne nom_plat non trouvée, utilisation de la valeur par défaut");
                            }

                            try {
                                prix = rsPlats.getDouble("prix");
                            } catch (SQLException e) {
                                System.out.println("Colonne prix non trouvée, utilisation de la valeur par défaut");
                            }

                            Plat plat = new Plat(nomPlat, prix, "");
                            commande.ajouterMenu(plat);
                            platCount++;
                        }

                        System.out.println("Commande #" + id + " récupérée avec " + platCount + " plats");
                    } catch (SQLException e) {
                        System.out.println("Erreur lors de la récupération des plats pour la commande #" + id + ": " + e.getMessage());
                    }

                    commandes.add(commande);
                    count++;
                } catch (SQLException e) {
                    System.out.println("Erreur lors de la récupération de la commande: " + e.getMessage());
                }
            }

            System.out.println(count + " commandes au total récupérées");
            conn.close();

        } catch (SQLException e) {
            System.err.println("ERREUR SQL lors de la récupération de toutes les commandes: " + e.getMessage());
            e.printStackTrace();
        }

        return commandes;
    }

    public boolean updateCommandePayee(int commandeId, boolean payee) {
        if (!dbAvailable) {
            System.err.println("Base de données non disponible. La commande ne sera pas mise à jour.");
            // Simuler un succès pour ne pas bloquer l'application
            return true;
        }

        try {
            Connection conn = DriverManager.getConnection(dbUrl, user, password);
            System.out.println("Connexion établie pour mettre à jour le statut de paiement de la commande #" + commandeId);

            // Vérifier les noms des colonnes dans la table commandes
            DatabaseMetaData metaData = conn.getMetaData();
            ResultSet columns = metaData.getColumns(null, null, "commandes", null);

            // Stockage des noms de colonnes pour vérification
            List<String> columnNames = new ArrayList<>();
            while (columns.next()) {
                columnNames.add(columns.getString("COLUMN_NAME").toLowerCase());
            }

            System.out.println("Colonnes disponibles dans la table commandes: " + columnNames);

            // Trouver la colonne pour le statut de paiement
            String payeeColumn = columnNames.contains("payee") ? "payee" :
                               columnNames.contains("paye") ? "paye" :
                               columnNames.contains("paid") ? "paid" : null;

            if (payeeColumn == null) {
                System.err.println("Aucune colonne de paiement trouvée dans la table commandes");
                conn.close();
                return false;
            }

            String sql = "UPDATE commandes SET " + payeeColumn + " = ? WHERE id = ?";
            System.out.println("Exécution de la requête: " + sql);

            PreparedStatement pstmt = conn.prepareStatement(sql);

            pstmt.setBoolean(1, payee);
            pstmt.setInt(2, commandeId);

            int rowsAffected = pstmt.executeUpdate();
            conn.close();

            if (rowsAffected > 0) {
                System.out.println("Statut de paiement de la commande #" + commandeId + " mis à jour avec succès: " + (payee ? "payée" : "non payée"));
                return true;
            } else {
                System.out.println("Aucune commande trouvée avec l'ID " + commandeId);
                return false;
            }

        } catch (SQLException e) {
            System.err.println("ERREUR SQL lors de la mise à jour du statut de paiement: " + e.getMessage());
            e.printStackTrace();
            return false;
        }
    }

    // Méthodes pour les statistiques

    // Récupérer les ventes par jour pour les 31 derniers jours
    public Map<String, Double> getVentesParJour() {
        Map<String, Double> ventesParJour = new LinkedHashMap<>(); // LinkedHashMap pour conserver l'ordre

        try (Connection conn = DriverManager.getConnection(dbUrl, user, password)) {
            PreparedStatement pstmt = conn.prepareStatement(
                    "SELECT DATE(date_commande) as jour, SUM(total) as total_jour " +
                    "FROM commandes " +
                    "WHERE date_commande >= DATE_SUB(CURDATE(), INTERVAL 30 DAY) " +
                    "GROUP BY jour " +
                    "ORDER BY jour ASC");

            ResultSet rs = pstmt.executeQuery();

            while (rs.next()) {
                String jour = rs.getString("jour");
                double totalJour = rs.getDouble("total_jour");
                ventesParJour.put(jour, totalJour);
            }

        } catch (SQLException e) {
            e.printStackTrace();
        }

        return ventesParJour;
    }

    // Récupérer les ventes par mois pour les 24 derniers mois
    public Map<String, Double> getVentesParMois() {
        Map<String, Double> ventesParMois = new LinkedHashMap<>(); // LinkedHashMap pour conserver l'ordre

        try (Connection conn = DriverManager.getConnection(dbUrl, user, password)) {
            PreparedStatement pstmt = conn.prepareStatement(
                    "SELECT DATE_FORMAT(date_commande, '%Y-%m') as mois, SUM(total) as total_mois " +
                    "FROM commandes " +
                    "WHERE date_commande >= DATE_SUB(CURDATE(), INTERVAL 24 MONTH) " +
                    "GROUP BY mois " +
                    "ORDER BY mois ASC");

            ResultSet rs = pstmt.executeQuery();

            while (rs.next()) {
                String mois = rs.getString("mois");
                double totalMois = rs.getDouble("total_mois");
                ventesParMois.put(mois, totalMois);
            }

        } catch (SQLException e) {
            e.printStackTrace();
        }

        return ventesParMois;
    }

    // Récupérer le nombre de commandes par jour pour les 31 derniers jours
    public Map<String, Integer> getCommandesParJour() {
        Map<String, Integer> commandesParJour = new LinkedHashMap<>(); // LinkedHashMap pour conserver l'ordre

        try (Connection conn = DriverManager.getConnection(dbUrl, user, password)) {
            PreparedStatement pstmt = conn.prepareStatement(
                    "SELECT DATE(date_commande) as jour, COUNT(*) as nb_commandes " +
                    "FROM commandes " +
                    "WHERE date_commande >= DATE_SUB(CURDATE(), INTERVAL 30 DAY) " +
                    "GROUP BY jour " +
                    "ORDER BY jour ASC");

            ResultSet rs = pstmt.executeQuery();

            while (rs.next()) {
                String jour = rs.getString("jour");
                int nbCommandes = rs.getInt("nb_commandes");
                commandesParJour.put(jour, nbCommandes);
            }

        } catch (SQLException e) {
            e.printStackTrace();
        }

        return commandesParJour;
    }

    // Récupérer le nombre de commandes par mois pour les 24 derniers mois
    public Map<String, Integer> getCommandesParMois() {
        Map<String, Integer> commandesParMois = new LinkedHashMap<>(); // LinkedHashMap pour conserver l'ordre

        try (Connection conn = DriverManager.getConnection(dbUrl, user, password)) {
            PreparedStatement pstmt = conn.prepareStatement(
                    "SELECT DATE_FORMAT(date_commande, '%Y-%m') as mois, COUNT(*) as nb_commandes " +
                    "FROM commandes " +
                    "WHERE date_commande >= DATE_SUB(CURDATE(), INTERVAL 24 MONTH) " +
                    "GROUP BY mois " +
                    "ORDER BY mois ASC");

            ResultSet rs = pstmt.executeQuery();

            while (rs.next()) {
                String mois = rs.getString("mois");
                int nbCommandes = rs.getInt("nb_commandes");
                commandesParMois.put(mois, nbCommandes);
            }

        } catch (SQLException e) {
            e.printStackTrace();
        }

        return commandesParMois;
    }
}